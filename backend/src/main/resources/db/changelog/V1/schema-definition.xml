<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- Create Site table -->
    <changeSet id="001-create-site-table" author="abounass">
        <createTable tableName="sites">
            <column name="site_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="city" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="country" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <!-- Audit fields from BaseEntity -->
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_selected_at" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Create indexes for better performance -->
        <createIndex tableName="sites" indexName="idx_site_name">
            <column name="name"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_city">
            <column name="city"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_country">
            <column name="country"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_active">
            <column name="is_active"/>
        </createIndex>
    </changeSet>



    <!-- Create spaces table -->
    <changeSet id="3" author="abounass">
        <createTable tableName="spaces">
            <column name="space_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="site_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="type" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="capacity" type="INT">
                <constraints nullable="true"/>
            </column>
            <column name="location" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="area" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="images" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="rules" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="hourly_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="daily_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="weekly_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="monthly_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="floor" type="VARCHAR(50)" defaultValue="GROUND_FLOOR">
                <constraints nullable="false"/>
            </column>
            <column name="amenities" type="VARCHAR(1000)">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Add foreign key constraint to sites -->
        <addForeignKeyConstraint
                baseTableName="spaces"
                baseColumnNames="site_id"
                constraintName="fk_spaces_site_id"
                referencedTableName="sites"
                referencedColumnNames="site_id"
                onDelete="CASCADE"/>

        <!-- Create indexes for spaces -->
        <createIndex tableName="spaces" indexName="idx_space_site_id">
            <column name="site_id"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_name">
            <column name="name"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_type">
            <column name="type"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_active">
            <column name="is_active"/>
        </createIndex>
    </changeSet>



    <!-- Create equipment table -->
    <changeSet id="5" author="abounass">
        <createTable tableName="equipment">
            <column name="equipment_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="brand" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="model" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="quantity" type="INT" defaultValueNumeric="1">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(50)" defaultValue="WORKING">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Foreign key constraint -->
        <addForeignKeyConstraint
            baseTableName="equipment"
            baseColumnNames="space_id"
            constraintName="fk_equipment_space"
            referencedTableName="spaces"
            referencedColumnNames="space_id"
            onDelete="CASCADE"/>

        <!-- Create indexes -->
        <createIndex tableName="equipment" indexName="idx_equipment_space_id">
            <column name="space_id"/>
        </createIndex>
        <createIndex tableName="equipment" indexName="idx_equipment_type">
            <column name="type"/>
        </createIndex>
        <createIndex tableName="equipment" indexName="idx_equipment_status">
            <column name="status"/>
        </createIndex>
    </changeSet>

    <!-- Create space_availability table -->
    <changeSet id="6" author="abounass">
        <createTable tableName="space_availability">
            <column name="availability_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="advance_booking_days" type="INT" defaultValueNumeric="30">
                <constraints nullable="false"/>
            </column>
            <column name="min_booking_duration" type="INT" defaultValueNumeric="60">
                <constraints nullable="false"/>
            </column>
            <column name="max_booking_duration" type="INT" defaultValueNumeric="480">
                <constraints nullable="false"/>
            </column>
            <column name="buffer_time" type="INT" defaultValueNumeric="15">
                <constraints nullable="false"/>
            </column>
            <column name="weekly_schedule" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="exceptions" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Foreign key constraint -->
        <addForeignKeyConstraint
            baseTableName="space_availability"
            baseColumnNames="space_id"
            constraintName="fk_availability_space"
            referencedTableName="spaces"
            referencedColumnNames="space_id"
            onDelete="CASCADE"/>

        <!-- Create index -->
        <createIndex tableName="space_availability" indexName="idx_availability_space_id">
            <column name="space_id"/>
        </createIndex>
    </changeSet>

    <!-- Create space_pricing table -->
    <changeSet id="7" author="abounass">
        <createTable tableName="space_pricing">
            <column name="pricing_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hourly_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="daily_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="weekly_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="monthly_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(10)" defaultValue="EUR">
                <constraints nullable="false"/>
            </column>
            <column name="discounts" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Foreign key constraint -->
        <addForeignKeyConstraint
            baseTableName="space_pricing"
            baseColumnNames="space_id"
            constraintName="fk_pricing_space"
            referencedTableName="spaces"
            referencedColumnNames="space_id"
            onDelete="CASCADE"/>

        <!-- Create index -->
        <createIndex tableName="space_pricing" indexName="idx_pricing_space_id">
            <column name="space_id"/>
        </createIndex>
    </changeSet>



    <!-- Create Subscriptions table -->
    <changeSet id="008-create-subscriptions-table" author="abounass">
        <createTable tableName="subscriptions">
            <column name="subscription_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="site_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="price" type="DECIMAL(10,2)">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(10)" defaultValue="MAD">
                <constraints nullable="false"/>
            </column>
            <column name="duration_days" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="member_type" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="features" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="max_reservations_per_day" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="max_reservations_per_week" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="max_reservations_per_month" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="max_consecutive_hours" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="advance_booking_days" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="can_book_meeting_rooms" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="can_access_premium_areas" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <!-- Audit fields from BaseEntity -->
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Add foreign key constraint to sites -->
        <addForeignKeyConstraint
                baseTableName="subscriptions"
                baseColumnNames="site_id"
                constraintName="fk_subscriptions_site_id"
                referencedTableName="sites"
                referencedColumnNames="site_id"
                onDelete="CASCADE"/>

        <!-- Create indexes for better performance -->
        <createIndex tableName="subscriptions" indexName="idx_subscription_site_id">
            <column name="site_id"/>
        </createIndex>

        <createIndex tableName="subscriptions" indexName="idx_subscription_member_type">
            <column name="member_type"/>
        </createIndex>

        <createIndex tableName="subscriptions" indexName="idx_subscription_active">
            <column name="is_active"/>
        </createIndex>
    </changeSet>

    <!-- Create Members table -->
    <changeSet id="009-create-members-table" author="abounass">
        <createTable tableName="members">
            <column name="member_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="site_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="first_name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="phone" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="company" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="member_type" type="VARCHAR(20)" defaultValue="PROFESSIONAL">
                <constraints nullable="false"/>
            </column>
            <column name="student_code" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="ice_number" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="subscription_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="status" type="VARCHAR(20)" defaultValue="ACTIVE">
                <constraints nullable="false"/>
            </column>
            <!-- Audit fields from BaseEntity -->
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Add foreign key constraints -->
        <addForeignKeyConstraint
                baseTableName="members"
                baseColumnNames="site_id"
                constraintName="fk_members_site_id"
                referencedTableName="sites"
                referencedColumnNames="site_id"
                onDelete="CASCADE"/>

        <addForeignKeyConstraint
                baseTableName="members"
                baseColumnNames="subscription_id"
                constraintName="fk_members_subscription_id"
                referencedTableName="subscriptions"
                referencedColumnNames="subscription_id"
                onDelete="SET NULL"/>

        <!-- Create indexes -->
        <createIndex tableName="members" indexName="idx_member_site_id">
            <column name="site_id"/>
        </createIndex>

        <createIndex tableName="members" indexName="idx_member_email">
            <column name="email"/>
        </createIndex>

        <createIndex tableName="members" indexName="idx_member_type">
            <column name="member_type"/>
        </createIndex>

        <createIndex tableName="members" indexName="idx_student_code">
            <column name="student_code"/>
        </createIndex>

        <createIndex tableName="members" indexName="idx_ice_number">
            <column name="ice_number"/>
        </createIndex>

        <createIndex tableName="members" indexName="idx_subscription_id">
            <column name="subscription_id"/>
        </createIndex>
    </changeSet>

    <!-- Create Reservations table -->
    <changeSet id="010-create-reservations-table" author="abounass">
        <createTable tableName="reservations">
            <column name="reservation_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="site_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="member_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="start_time" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="end_time" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="purpose" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="status" type="VARCHAR(20)" defaultValue="PENDING">
                <constraints nullable="false"/>
            </column>
            <column name="number_of_people" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="notes" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="recurrence" type="VARCHAR(20)" defaultValue="NONE">
                <constraints nullable="true"/>
            </column>
            <column name="total_cost" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <!-- Audit fields from BaseEntity -->
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Add foreign key constraints -->
        <addForeignKeyConstraint
                baseTableName="reservations"
                baseColumnNames="site_id"
                constraintName="fk_reservations_site_id"
                referencedTableName="sites"
                referencedColumnNames="site_id"
                onDelete="CASCADE"/>

        <addForeignKeyConstraint
                baseTableName="reservations"
                baseColumnNames="space_id"
                constraintName="fk_reservation_space"
                referencedTableName="spaces"
                referencedColumnNames="space_id"
                onDelete="CASCADE"/>

        <addForeignKeyConstraint
                baseTableName="reservations"
                baseColumnNames="member_id"
                constraintName="fk_reservation_member"
                referencedTableName="members"
                referencedColumnNames="member_id"
                onDelete="CASCADE"/>

        <!-- Create indexes for better performance -->
        <createIndex tableName="reservations" indexName="idx_reservation_site_id">
            <column name="site_id"/>
        </createIndex>

        <createIndex tableName="reservations" indexName="idx_reservation_space_id">
            <column name="space_id"/>
        </createIndex>

        <createIndex tableName="reservations" indexName="idx_reservation_member_id">
            <column name="member_id"/>
        </createIndex>

        <createIndex tableName="reservations" indexName="idx_reservation_status">
            <column name="status"/>
        </createIndex>

        <createIndex tableName="reservations" indexName="idx_reservation_start_time">
            <column name="start_time"/>
        </createIndex>

        <createIndex tableName="reservations" indexName="idx_reservation_date_range">
            <column name="start_time"/>
            <column name="end_time"/>
        </createIndex>
    </changeSet>











</databaseChangeLog>