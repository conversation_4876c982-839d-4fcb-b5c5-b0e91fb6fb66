<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.24.xsd">

    <!-- Insert sample data -->
    <changeSet id="002-insert-sample-sites" author="abounass">
        <insert tableName="sites">
            <column name="name" value="Workeem Paris"/>
            <column name="city" value="Paris"/>
            <column name="country" value="France"/>
            <column name="address" value="123 Avenue des Champs-Élysées, 75008 Paris"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Lyon"/>
            <column name="city" value="Lyon"/>
            <column name="country" value="France"/>
            <column name="address" value="45 Rue de la République, 69002 Lyon"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Marseille"/>
            <column name="city" value="Marseille"/>
            <column name="country" value="France"/>
            <column name="address" value="78 La Canebière, 13001 Marseille"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Bordeaux"/>
            <column name="city" value="Bordeaux"/>
            <column name="country" value="France"/>
            <column name="address" value="12 Cours de l'Intendance, 33000 Bordeaux"/>
            <column name="is_active" valueBoolean="false"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>
    </changeSet>

    <!-- Insert sample spaces data -->
    <changeSet id="8" author="abounass">
        <!-- Poste de travail A1 -->
        <insert tableName="spaces">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Poste de travail A1"/>
            <column name="description" value="Poste de travail individuel avec vue sur jardin"/>
            <column name="type" value="WORKSTATION"/>
            <column name="capacity" valueNumeric="1"/>
            <column name="location" value="Zone A"/>
            <column name="floor" value="GROUND_FLOOR"/>
            <column name="area" valueNumeric="4.0"/>
            <column name="amenities" value="WiFi,Prise électrique,Éclairage LED"/>
            <column name="rules" value="Maintenir l'espace propre,Pas de nourriture,Silence requis"/>
            <column name="hourly_rate" valueNumeric="8.0"/>
            <column name="daily_rate" valueNumeric="50.0"/>
            <column name="weekly_rate" valueNumeric="300.0"/>
            <column name="monthly_rate" valueNumeric="1000.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Bureau privé B1 -->
        <insert tableName="spaces">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Bureau privé B1"/>
            <column name="description" value="Bureau privé pour 2 personnes avec équipement complet"/>
            <column name="type" value="PRIVATE_OFFICE"/>
            <column name="capacity" valueNumeric="2"/>
            <column name="location" value="Zone B"/>
            <column name="floor" value="FIRST_FLOOR"/>
            <column name="area" valueNumeric="12.0"/>
            <column name="amenities" value="WiFi,Climatisation,Fenêtre,Tableau blanc"/>
            <column name="rules" value="Accès par badge,Nettoyage quotidien inclus,Pas de visiteurs sans autorisation"/>
            <column name="hourly_rate" valueNumeric="25.0"/>
            <column name="daily_rate" valueNumeric="180.0"/>
            <column name="weekly_rate" valueNumeric="1000.0"/>
            <column name="monthly_rate" valueNumeric="3500.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Salle de réunion Alpha -->
        <insert tableName="spaces">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Salle de réunion Alpha"/>
            <column name="description" value="Salle de réunion moderne pour 8 personnes avec équipement audiovisuel"/>
            <column name="type" value="MEETING_ROOM"/>
            <column name="capacity" valueNumeric="8"/>
            <column name="location" value="Zone C"/>
            <column name="floor" value="SECOND_FLOOR"/>
            <column name="area" valueNumeric="25.0"/>
            <column name="amenities" value="WiFi haut débit,Climatisation,Éclairage modulable,Isolation phonique"/>
            <column name="rules" value="Réservation obligatoire,Nettoyage après usage,Matériel à remettre en place"/>
            <column name="hourly_rate" valueNumeric="45.0"/>
            <column name="daily_rate" valueNumeric="320.0"/>
            <column name="weekly_rate" valueNumeric="1800.0"/>
            <column name="monthly_rate" valueNumeric="6000.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Espace Collaboratif Principal -->
        <insert tableName="spaces">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Espace Collaboratif Principal"/>
            <column name="description" value="Grand espace ouvert pour le travail collaboratif, accessible sans réservation"/>
            <column name="type" value="COLLABORATIVE"/>
            <column name="capacity" valueNumeric="40"/>
            <column name="location" value="Rez-de-chaussée"/>
            <column name="floor" value="GROUND_FLOOR"/>
            <column name="area" valueNumeric="120.0"/>
            <column name="amenities" value="WiFi haut débit,Prises électriques multiples,Éclairage naturel,Mobilier ergonomique,Espaces détente,Machine à café,Imprimante partagée"/>
            <column name="rules" value="Accès libre pendant les heures d'ouverture,Respect du silence relatif,Nettoyage après usage"/>
            <column name="hourly_rate" valueNumeric="0.0"/>
            <column name="daily_rate" valueNumeric="0.0"/>
            <column name="weekly_rate" valueNumeric="0.0"/>
            <column name="monthly_rate" valueNumeric="0.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Cabine téléphonique 1 -->
        <insert tableName="spaces">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Cabine téléphonique 1"/>
            <column name="description" value="Cabine isolée pour appels confidentiels"/>
            <column name="type" value="PHONE_BOOTH"/>
            <column name="capacity" valueNumeric="1"/>
            <column name="location" value="Zone A"/>
            <column name="floor" value="GROUND_FLOOR"/>
            <column name="area" valueNumeric="2.0"/>
            <column name="amenities" value="Isolation phonique,Éclairage,Ventilation"/>
            <column name="rules" value="Durée maximale 30 minutes,Pas de nourriture"/>
            <column name="hourly_rate" valueNumeric="5.0"/>
            <column name="daily_rate" valueNumeric="30.0"/>
            <column name="weekly_rate" valueNumeric="150.0"/>
            <column name="monthly_rate" valueNumeric="500.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>
    </changeSet>

    <!-- Insert test members data -->
    <changeSet id="012-insert-test-members" author="abounass">
        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Aicha"/>
            <column name="last_name" value="Benali"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 12 34 56 78"/>
            <column name="company" value=""/>
            <column name="member_type" value="STUDENT"/>
            <column name="student_code" value="ETU2024001"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-01-15"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-01-15"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Omar"/>
            <column name="last_name" value="El Fassi"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 98 76 54 32"/>
            <column name="company" value="TechCorp"/>
            <column name="member_type" value="COMPANY"/>
            <column name="ice_number" value="ICE000000002"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-02-01"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-02-01"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Fatima"/>
            <column name="last_name" value="Zahra"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 11 22 33 44"/>
            <column name="company" value=""/>
            <column name="member_type" value="STUDENT"/>
            <column name="student_code" value="ETU2024003"/>
            <column name="status" value="INACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-01-20"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-01-25"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Houssam"/>
            <column name="last_name" value="Alaoui"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 55 44 33 22"/>
            <column name="company" value="InnovateMA"/>
            <column name="member_type" value="COMPANY"/>
            <column name="ice_number" value="ICE000000004"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-02-10"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-02-10"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Khadija"/>
            <column name="last_name" value="Benjelloun"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 77 88 99 00"/>
            <column name="company" value=""/>
            <column name="member_type" value="STUDENT"/>
            <column name="student_code" value="ETU2024005"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-02-15"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-02-15"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Hassan"/>
            <column name="last_name" value="Tazi"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 33 22 11 00"/>
            <column name="company" value="FreelanceMA"/>
            <column name="member_type" value="PROFESSIONAL"/>
            <column name="status" value="INACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-01-30"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-02-20"/>
        </insert>

        <!-- Members pour le site 2 pour tester le changement de site -->
        <insert tableName="members">
            <column name="site_id" valueNumeric="2"/>
            <column name="first_name" value="Youssef"/>
            <column name="last_name" value="Amrani"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 44 55 66 77"/>
            <column name="company" value=""/>
            <column name="member_type" value="STUDENT"/>
            <column name="student_code" value="ETU2024006"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-03-01"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-03-01"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="2"/>
            <column name="first_name" value="Salma"/>
            <column name="last_name" value="Berrada"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 88 99 00 11"/>
            <column name="company" value="BusinessCorp"/>
            <column name="member_type" value="COMPANY"/>
            <column name="ice_number" value="ICE000000007"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-03-05"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-03-05"/>
        </insert>
    </changeSet>

    <!-- Insert default subscriptions data -->
    <changeSet id="011-insert-default-subscriptions" author="abounass">
        <!-- Abonnements pour étudiants -->
        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Étudiant Basique"/>
            <column name="description" value="Abonnement basique pour étudiants avec accès limité"/>
            <column name="price" valueNumeric="50.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="30"/>
            <column name="member_type" value="STUDENT"/>
            <column name="features" value="Accès espaces de coworking,WiFi gratuit,2h par jour maximum"/>
            <column name="max_reservations_per_day" valueNumeric="1"/>
            <column name="max_reservations_per_week" valueNumeric="5"/>
            <column name="max_reservations_per_month" valueNumeric="20"/>
            <column name="max_consecutive_hours" valueNumeric="2"/>
            <column name="advance_booking_days" valueNumeric="7"/>
            <column name="can_book_meeting_rooms" valueBoolean="false"/>
            <column name="can_access_premium_areas" valueBoolean="false"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Étudiant Premium"/>
            <column name="description" value="Abonnement premium pour étudiants avec plus d'avantages"/>
            <column name="price" valueNumeric="80.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="30"/>
            <column name="member_type" value="STUDENT"/>
            <column name="features" value="Accès espaces de coworking,WiFi gratuit,4h par jour maximum,Accès salles de réunion"/>
            <column name="max_reservations_per_day" valueNumeric="2"/>
            <column name="max_reservations_per_week" valueNumeric="10"/>
            <column name="max_reservations_per_month" valueNumeric="40"/>
            <column name="max_consecutive_hours" valueNumeric="4"/>
            <column name="advance_booking_days" valueNumeric="14"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="false"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Abonnements pour professionnels -->
        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Professionnel Standard"/>
            <column name="description" value="Abonnement standard pour professionnels"/>
            <column name="price" valueNumeric="120.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="30"/>
            <column name="member_type" value="PROFESSIONAL"/>
            <column name="features" value="Accès illimité espaces de coworking,WiFi gratuit,Salles de réunion,Support technique"/>
            <column name="max_reservations_per_day" valueNumeric="3"/>
            <column name="max_reservations_per_week" valueNumeric="15"/>
            <column name="max_reservations_per_month" valueNumeric="60"/>
            <column name="max_consecutive_hours" valueNumeric="8"/>
            <column name="advance_booking_days" valueNumeric="30"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="true"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Professionnel Premium"/>
            <column name="description" value="Abonnement premium pour professionnels avec tous les avantages"/>
            <column name="price" valueNumeric="200.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="30"/>
            <column name="member_type" value="PROFESSIONAL"/>
            <column name="features" value="Accès illimité,WiFi gratuit,Salles de réunion prioritaires,Support technique premium,Espaces privés"/>
            <column name="max_reservations_per_day" valueNumeric="5"/>
            <column name="max_reservations_per_week" valueNumeric="25"/>
            <column name="max_reservations_per_month" valueNumeric="100"/>
            <column name="max_consecutive_hours" valueNumeric="12"/>
            <column name="advance_booking_days" valueNumeric="60"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="true"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Abonnements pour entreprises -->
        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Entreprise Domiciliation"/>
            <column name="description" value="Abonnement pour domiciliation d'entreprise"/>
            <column name="price" valueNumeric="300.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="30"/>
            <column name="member_type" value="COMPANY"/>
            <column name="features" value="Adresse commerciale,Réception courrier,Salles de réunion,Espaces de coworking,Support administratif"/>
            <column name="max_reservations_per_day" valueNumeric="10"/>
            <column name="max_reservations_per_week" valueNumeric="50"/>
            <column name="max_reservations_per_month" valueNumeric="200"/>
            <column name="max_consecutive_hours" valueNumeric="24"/>
            <column name="advance_booking_days" valueNumeric="90"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="true"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

    </changeSet>

</databaseChangeLog>