package com.workeem.workeem_api;

import com.workeem.workeem_api.multitenancy.master.infrastructure.TenantContext;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Order(1)
public class TenantFilter implements Filter {

    @Value("${multitenancy.http.headerName}")
    private String tenantHeader;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;

        // Skip tenant filtering only for tenant verification endpoint
        String requestURI = httpRequest.getRequestURI();
        if (requestURI.startsWith("/api/tenants/verify")) {
            chain.doFilter(request, response);
            return;
        }

        // Check if user has SUPER_ADMIN role - if so, skip tenant filtering
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            boolean isSuperAdmin = authentication.getAuthorities().stream()
                    .map(GrantedAuthority::getAuthority)
                    .anyMatch(authority -> "ROLE_SUPER_ADMIN".equals(authority));

            if (isSuperAdmin) {
                // Super admin bypasses tenant filtering
                chain.doFilter(request, response);
                return;
            }
        }

        // Retrieve tenant identifier from HTTP header
        String tenantName = httpRequest.getHeader(tenantHeader);

        if (tenantName == null || tenantName.isEmpty()) {
            // Optional: throw an exception or define a default tenant
            throw new ServletException("Header '" + tenantHeader + "' is missing or empty.");
        }

        // Set current tenant in context
        TenantContext.setCurrentTenant(tenantName);

        try {
            // Continue filter chain
            chain.doFilter(request, response);
        } finally {
            // Always clean context after processing
            TenantContext.clear();
        }
    }
}
