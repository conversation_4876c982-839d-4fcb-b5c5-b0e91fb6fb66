package com.workeem.workeem_api.shared.security;

/**
 * Classe centralisée pour gérer les autorisations par plan
 * 
 * <AUTHOR>
 */
public final class PlanAuthorizations {

    private PlanAuthorizations() {
        // Classe utilitaire - constructeur privé
    }

    // ========== PLANS DISPONIBLES ==========
    
    /**
     * Tous les plans - Fonctionnalités de base
     * Inclut : Gestion des espaces, membres, réservations
     */
    public static final String ALL_PLANS = 
        "hasRole('BASIC_PLAN') or hasRole('STARTER_PLAN') or hasRole('PROFESSIONAL_PLAN') or hasRole('ENTREPRISE_PLAN')";

    /**
     * Plans avec abonnements - Exclut BASIC_PLAN
     * Inclut : Gestion des abonnements, facturation, statistiques
     */
    public static final String SUBSCRIPTION_PLANS = 
        "hasRole('STARTER_PLAN') or hasRole('PROFESSIONAL_PLAN') or hasRole('ENTREPRISE_PLAN')";

    /**
     * Plans avec multi-site - Seulement les plans avancés
     * Inclut : Gestion multi-site, fonctionnalités avancées
     */
    public static final String MULTI_SITE_PLANS = 
        "hasRole('PROFESSIONAL_PLAN') or hasRole('ENTREPRISE_PLAN')";

    /**
     * Plan entreprise uniquement - Fonctionnalités premium
     */
    public static final String ENTERPRISE_PLAN_ONLY =
        "hasRole('ENTREPRISE_PLAN')";

    /**
     * Super administrateur - Accès complet au système
     */
    public static final String SUPER_ADMIN_ONLY =
        "hasRole('SUPER_ADMIN')";

    // ========== FONCTIONNALITÉS SPÉCIFIQUES ==========

    /**
     * Gestion des espaces - Tous les plans
     */
    public static final String CAN_MANAGE_SPACES = ALL_PLANS;

    /**
     * Gestion des membres - Tous les plans
     */
    public static final String CAN_MANAGE_MEMBERS = ALL_PLANS;

    /**
     * Gestion des réservations - Tous les plans
     */
    public static final String CAN_MANAGE_RESERVATIONS = ALL_PLANS;

    /**
     * Gestion des abonnements - Sauf BASIC_PLAN
     */
    public static final String CAN_MANAGE_SUBSCRIPTIONS = SUBSCRIPTION_PLANS;

    /**
     * Accès à la facturation - Sauf BASIC_PLAN
     */
    public static final String CAN_ACCESS_BILLING = SUBSCRIPTION_PLANS;

    /**
     * Accès aux statistiques - Sauf BASIC_PLAN
     */
    public static final String CAN_ACCESS_STATISTICS = SUBSCRIPTION_PLANS;

    /**
     * Gestion multi-site - Plans avancés uniquement
     */
    public static final String CAN_MANAGE_MULTI_SITE = MULTI_SITE_PLANS;

    /**
     * Création de sites - Plans avancés uniquement
     */
    public static final String CAN_CREATE_SITES = MULTI_SITE_PLANS;

    /**
     * Modification de sites - Plans avancés uniquement
     */
    public static final String CAN_MODIFY_SITES = MULTI_SITE_PLANS;

    /**
     * Suppression de sites - Plans avancés uniquement
     */
    public static final String CAN_DELETE_SITES = MULTI_SITE_PLANS;
}
