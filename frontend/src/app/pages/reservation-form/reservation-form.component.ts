import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { fork<PERSON>oin } from 'rxjs';

import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NgxPermissionsModule } from 'ngx-permissions';

import {EquipmentStatus, Space, SpaceStatus} from '../../models/space.model';
import { SpaceService } from '../../services/space.service';
import { Reservation } from '../../models/reservation.model';
import { ReservationService } from '../../services/reservation.service';
import { MemberService } from '../../services/member.service';
import { SubscriptionService } from '../../services/subscription.service';
import { UiPermissionsService } from '../../services/ui-permissions.service';
import { ErrorService, ValidationMessages } from '../../services/error.service';
import { Member, getMemberFullName, MemberType } from '../../models/member.model';
import { SubscriptionPlan } from '../../models/subscription.model';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-reservation-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzSelectModule,
    NzDatePickerModule,
    NzTimePickerModule,
    NzButtonModule,
    NzCardModule,
    NzIconModule,
    NzTagModule,
    NzGridModule,
    NzRadioModule,
    NzInputNumberModule,
    NzAlertModule,
    NzMessageModule,
    NgxPermissionsModule
  ],
  templateUrl: './reservation-form.component.html',
  styleUrl: './reservation-form.component.css'
})
export class ReservationFormComponent implements OnInit {

  // Signaux pour la réactivité
  private loadingSignal = signal<boolean>(false);
  private savingSignal = signal<boolean>(false);
  private spacesSignal = signal<Space[]>([]);
  private membersSignal = signal<Member[]>([]);

  // Getters pour les templates
  get loading() { return this.loadingSignal(); }
  get saving() { return this.savingSignal(); }
  get spaces() { return this.spacesSignal(); }
  get members() { return this.membersSignal(); }

  // Formulaire
  reservationForm!: FormGroup;
  isEditMode = false;
  selectedSpace: Space | null = null;
  editingReservationId: string | null = null;
  editingReservation: Reservation | null = null;

  // Enums pour les templates
  MemberType = MemberType;

  // Données des abonnements
  subscriptions: SubscriptionPlan[] = [];

  // Options pour les listes déroulantes
  statusOptions = [
    { value: 'confirmed', label: 'Confirmée' },
    { value: 'pending', label: 'En attente' },
    { value: 'cancelled', label: 'Annulée' }
  ];

  recurrenceOptions = [
    { value: 'none', label: 'Aucune récurrence' },
    { value: 'daily', label: 'Quotidienne' },
    { value: 'weekly', label: 'Hebdomadaire' },
    { value: 'monthly', label: 'Mensuelle' }
  ];

  validation_messages: ValidationMessages = {
    'spaceId': [
      { type: 'required', message: 'L\'espace est obligatoire' }
    ],
    'startDate': [
      { type: 'required', message: 'La date et heure de début sont obligatoires' }
    ],
    'endDate': [
      { type: 'required', message: 'La date et heure de fin sont obligatoires' }
    ],
    'memberType': [
      { type: 'required', message: 'Le type de membre est obligatoire' }
    ],
    'existingMemberId': [
      { type: 'required', message: 'Veuillez sélectionner un membre' },
      { type: 'memberNotFound', message: 'Membre sélectionné introuvable' }
    ],
    'firstName': [
      { type: 'required', message: 'Le prénom est obligatoire' }
    ],
    'lastName': [
      { type: 'required', message: 'Le nom est obligatoire' }
    ],
    'email': [
      { type: 'required', message: 'L\'email est obligatoire' },
      { type: 'email', message: 'Format d\'email invalide' }
    ],
    'phone': [
      { type: 'required', message: 'Le téléphone est obligatoire' },
      { type: 'pattern', message: 'Le téléphone doit être au format marocain (06xxxxxxxx, 07xxxxxxxx ou +212xxxxxxxx)' }
    ],
    'newMemberType': [
      { type: 'required', message: 'Le type de membre est obligatoire' }
    ]
  };

  constructor(
    private fb: FormBuilder,
    private spaceService: SpaceService,
    private router: Router,
    private route: ActivatedRoute,
    private reservationService: ReservationService,
    private memberService: MemberService,
    private subscriptionService: SubscriptionService,
    private message: NzMessageService,
    public uiPermissions: UiPermissionsService,
    private errorService: ErrorService
  ) {
    this.initializeForm();
  }

  ngOnInit() {
    // Charger les données de base d'abord
    this.loadSpaces();
    this.loadMembers();

    // Charger les abonnements seulement si l'utilisateur a les permissions
    if (this.uiPermissions.canShowSubscriptionColumn()) {
      this.loadSubscriptions();
    }

    // Traiter les paramètres de requête
    this.handleQueryParams();
    this.setDefaultDatesIfNeeded();
  }

  private handleQueryParams() {
    this.route.queryParams.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.editingReservationId = params['id'];
        this.loadReservation(params['id']);
      }

      if (params['date']) {
        const date = new Date(params['date']);
        // Définir des heures par défaut (9h00 - 10h00)
        const startDateTime = new Date(date);
        startDateTime.setHours(9, 0, 0, 0);

        const endDateTime = new Date(date);
        endDateTime.setHours(10, 0, 0, 0);

        this.reservationForm.patchValue({
          startDate: startDateTime,
          endDate: endDateTime
        });
      }

      if (params['spaceId']) {
        this.reservationForm.patchValue({
          spaceId: params['spaceId']
        });
        this.onSpaceChange(params['spaceId']);
      }

      // Traitement des nouveaux paramètres startTime et endTime
      if (params['startTime'] && params['endTime']) {
        // Créer les dates en heure locale pour éviter les problèmes de timezone
        const startTime = new Date(params['startTime']);
        const endTime = new Date(params['endTime']);

        console.log('Paramètres reçus:', {
          startTimeParam: params['startTime'],
          endTimeParam: params['endTime'],
          startTimeLocal: startTime.toString(),
          endTimeLocal: endTime.toString()
        });

        // Extraire la date en heure locale
        const reservationDate = new Date(startTime.getFullYear(), startTime.getMonth(), startTime.getDate());

        // Extraire les heures en heure locale
        const startHour = startTime.getHours();
        const startMinute = startTime.getMinutes();
        const endHour = endTime.getHours();
        const endMinute = endTime.getMinutes();

        console.log('Heures extraites:', {
          startHour,
          startMinute,
          endHour,
          endMinute
        });

        // Créer les objets Date pour les time pickers (aujourd'hui avec les bonnes heures)
        const today = new Date();
        const startTimeForPicker = new Date(today.getFullYear(), today.getMonth(), today.getDate(), startHour, startMinute);
        const endTimeForPicker = new Date(today.getFullYear(), today.getMonth(), today.getDate(), endHour, endMinute);

        // Calculer la durée en heures et minutes
        const durationInMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60);
        const durationHours = Math.floor(durationInMinutes / 60);
        const durationMinutesRemainder = durationInMinutes % 60;

        console.log('Valeurs pour le formulaire:', {
          date: reservationDate,
          startTime: startTimeForPicker,
          endTime: endTimeForPicker,
          durationHours: durationHours,
          durationMinutes: durationMinutesRemainder
        });

        // Mettre à jour le formulaire avec les dates complètes
        this.reservationForm.patchValue({
          startDate: startTime,
          endDate: endTime
        });
      }
    });
  }

  private setDefaultDatesIfNeeded() {
    // Si aucune date n'a été définie par les paramètres URL, utiliser des valeurs par défaut
    if (!this.reservationForm.get('startDate')?.value) {
      const now = new Date();
      const startDateTime = new Date(now);
      startDateTime.setHours(9, 0, 0, 0);

      const endDateTime = new Date(now);
      endDateTime.setHours(10, 0, 0, 0);

      this.reservationForm.patchValue({
        startDate: startDateTime,
        endDate: endDateTime
      });
    }
  }

  private loadMembers() {
    this.memberService.getMembers().subscribe({
      next: (members: Member[]) => {
        this.membersSignal.set(members);
      },
      error: (error: any) => {
        console.error('Error loading members:', error);
        this.message.error('Erreur lors du chargement des members');
      }
    });
  }

  private updateMemberValidations(memberType: string) {
    const existingMemberControl = this.reservationForm.get('existingMemberId');
    const firstNameControl = this.reservationForm.get('firstName');
    const lastNameControl = this.reservationForm.get('lastName');
    const emailControl = this.reservationForm.get('email');
    const phoneControl = this.reservationForm.get('phone');
    const newMemberTypeControl = this.reservationForm.get('newMemberType');

    if (memberType === 'existing') {
      // Member existant : seul l'ID est requis avec validation personnalisée
      existingMemberControl?.setValidators([
        Validators.required,
        (control) => {
          if (control.value && !this.members.find(m => m.id === control.value)) {
            return { memberNotFound: true };
          }
          return null;
        }
      ]);
      // Supprimer les validations pour les champs de nouveau membre
      firstNameControl?.clearValidators();
      lastNameControl?.clearValidators();
      emailControl?.clearValidators();
      phoneControl?.clearValidators();
      newMemberTypeControl?.clearValidators();
    } else {
      // Nouveau member : tous les champs requis
      existingMemberControl?.clearValidators();
      firstNameControl?.setValidators([Validators.required]);
      lastNameControl?.setValidators([Validators.required]);
      emailControl?.setValidators([Validators.required, Validators.email]);
      phoneControl?.setValidators([Validators.required, Validators.pattern(/^(06|07)\d{8}$|^\+212[67]\d{8}$/)]);
      newMemberTypeControl?.setValidators([Validators.required]);
    }

    // Mettre à jour les validations
    existingMemberControl?.updateValueAndValidity();
    firstNameControl?.updateValueAndValidity();
    lastNameControl?.updateValueAndValidity();
    emailControl?.updateValueAndValidity();
    phoneControl?.updateValueAndValidity();
    newMemberTypeControl?.updateValueAndValidity();
  }

  private loadReservation(id: string) {
    this.loadingSignal.set(true);

    // Charger la réservation et s'assurer que les membres sont chargés
    forkJoin({
      reservation: this.reservationService.getReservationById(id),
      members: this.memberService.getMembers(),
      spaces: this.spaceService.getSpaces()
    }).subscribe({
      next: ({ reservation, members, spaces }) => {
        if (reservation) {
          this.editingReservation = reservation;
          // Mettre à jour les signaux avec les données fraîches
          this.membersSignal.set(members);
          this.spacesSignal.set(spaces);
          // Populer le formulaire maintenant que toutes les données sont disponibles
          this.populateForm(reservation);
        } else {
          this.message.error('Réservation non trouvée');
          this.router.navigate(['/reservations']);
        }
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement de la réservation:', error);
        this.message.error('Erreur lors du chargement de la réservation');
        this.loadingSignal.set(false);
        this.router.navigate(['/reservations']);
      }
    });
  }

  private initializeForm() {
    const defaultStartTime = new Date();
    defaultStartTime.setHours(9, 0, 0, 0); // 9h00 par défaut

    const defaultEndTime = new Date();
    defaultEndTime.setHours(10, 0, 0, 0); // 10h00 par défaut

    this.reservationForm = this.fb.group({
      spaceId: ['', [Validators.required]],
      startDate: [null, [Validators.required]],
      endDate: [null, [Validators.required]],
      numberOfPeople: [1, [Validators.required, Validators.min(1)]],
      purpose: [''], // Rendu optionnel
      status: ['confirmed', [Validators.required]], // Statut de la réservation
      recurrence: ['none'],
      notes: [''],
      // Nouveaux champs pour le member
      memberType: ['existing', [Validators.required]], // 'existing' ou 'new'
      existingMemberId: [null], // Pas de valeur par défaut
      // Champs pour nouveau member
      firstName: [''],
      lastName: [''],
      email: [''],
      phone: [''],
      newMemberType: [''], // Type du nouveau membre (STUDENT, PROFESSIONAL, COMPANY)
      company: [''], // Nom de l'entreprise (si type COMPANY)
      studentCode: [''], // Code étudiant (si type STUDENT)
      iceNumber: [''], // Numéro ICE (si type COMPANY)
      subscriptionType: [''] // Toujours optionnel maintenant
    });

    // Appliquer les validations initiales
    this.updateMemberValidations('existing');

    // Gérer les validations conditionnelles pour le member
    this.reservationForm.get('memberType')?.valueChanges.subscribe((type) => {
      this.updateMemberValidations(type);
    });
  }

  private loadSpaces() {
    this.loadingSignal.set(true);
    this.spaceService.getSpaces().subscribe({
      next: (spaces) => {
        // Filtrer seulement les espaces disponibles
        const availableSpaces = spaces.filter(space => space.status === SpaceStatus.AVAILABLE);
        this.spacesSignal.set(availableSpaces);
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des espaces:', error);
        this.loadingSignal.set(false);
      }
    });
  }

  private populateForm(reservation: Reservation) {
    const memberId = reservation.memberId || reservation.userId;

    this.reservationForm.patchValue({
      spaceId: reservation.spaceId,
      startDate: new Date(reservation.startTime),
      endDate: new Date(reservation.endTime),
      numberOfPeople: reservation.numberOfPeople || 1,
      purpose: reservation.purpose || '',
      status: reservation.status,
      notes: reservation.notes || '',
      recurrence: reservation.recurrence || 'none',
      // Configurer pour membre existant
      memberType: 'existing',
      existingMemberId: memberId
    });



    // Sélectionner l'espace et déclencher les événements
    this.onSpaceChange(reservation.spaceId);
  }

  private calculateDuration(startTime: Date, endTime: Date): number {
    return Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));
  }

  // Méthodes d'événements du formulaire
  onSpaceChange(spaceId: string) {
    this.selectedSpace = this.spaces.find(space => space.id === spaceId) || null;
  }

  calculateDurationFromDates() {
    // Cette méthode ne fait plus rien car on n'a plus de champs de durée à mettre à jour
    // La durée est maintenant calculée à la volée dans getCalculatedDuration()
  }

  getCalculatedDuration(): string {
    const startDate = this.reservationForm.get('startDate')?.value;
    const endDate = this.reservationForm.get('endDate')?.value;

    if (!startDate || !endDate) {
      return '';
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (end <= start) {
      return '';
    }

    const diffTime = end.getTime() - start.getTime();

    // Calculer les mois, jours, heures et minutes
    const diffMinutes = Math.floor(diffTime / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    const diffMonths = Math.floor(diffDays / 30); // Approximation

    const remainingDays = diffDays % 30;
    const remainingHours = diffHours % 24;
    const remainingMinutes = diffMinutes % 60;

    let duration = '';

    if (diffMonths > 0) {
      duration += `${diffMonths} mois`;
    }

    if (remainingDays > 0) {
      if (duration) duration += ', ';
      duration += `${remainingDays} jour${remainingDays > 1 ? 's' : ''}`;
    }

    if (remainingHours > 0) {
      if (duration) duration += ', ';
      duration += `${remainingHours} heure${remainingHours > 1 ? 's' : ''}`;
    }

    if (remainingMinutes > 0) {
      if (duration) duration += ', ';
      duration += `${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}`;
    }

    return duration || '0 minute';
  }

  getDurationText(): string {
    const startDate = this.reservationForm.get('startDate')?.value;
    const endDate = this.reservationForm.get('endDate')?.value;

    if (!startDate || !endDate) {
      return '';
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Réservation sur la même journée';
    } else if (diffDays === 1) {
      return 'Réservation sur 1 jour';
    } else {
      return `Réservation sur ${diffDays} jours`;
    }
  }

  disabledEndDate = (endDate: Date): boolean => {
    const startDate = this.reservationForm.get('startDate')?.value;
    if (!startDate) {
      return false;
    }
    // Permettre la même date ou une date ultérieure
    const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
    const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());
    return endDateOnly < startDateOnly;
  };

  onEndTimeChange() {
    // Plus besoin de calculer la durée car elle est calculée à la volée
  }

  onStartTimeChange(time: Date) {
    // Plus besoin de calculer la durée car elle est calculée à la volée
  }

  // Méthodes de validation
  disabledDate = (current: Date): boolean => {
    // Désactiver les dates passées
    return current && current < new Date(new Date().setHours(0, 0, 0, 0));
  };

  disabledHours = (): number[] => {
    const disabled: number[] = [];

    // Ne pas désactiver les heures passées pour la journée courante
    // Le backend gère déjà cette validation correctement

    // Désactiver les heures en dehors des heures d'ouverture (8h-20h)
    for (let i = 0; i < 8; i++) {
      disabled.push(i);
    }
    for (let i = 20; i < 24; i++) {
      disabled.push(i);
    }

    return disabled;
  };

  disabledMinutes = (selectedHour: number): number[] => {
    // Ne pas désactiver les minutes passées pour la journée courante
    // Le backend gère déjà cette validation correctement

    // Permettre seulement les créneaux de 15 minutes
    return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59];
  };

  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  // Méthodes d'actions
  onSubmit() {
    if (this.reservationForm.valid) {
      this.savingSignal.set(true);

      try {
        const formValue = this.reservationForm.value;

        // Validation des champs requis
        if (!formValue.startDate || !formValue.endDate) {
          throw new Error('Date et heure de début et de fin sont requises');
        }

        const startDateTime = new Date(formValue.startDate);
        const endDateTime = new Date(formValue.endDate);

        // Validation que l'heure de fin est après l'heure de début
        if (endDateTime <= startDateTime) {
          throw new Error('L\'heure de fin doit être après l\'heure de début');
        }

      // Préparer les données pour l'API backend
      let userName = '';
      let userEmail = '';

      if (formValue.memberType === 'existing') {
        const selectedMember = this.members.find(c => c.id === formValue.existingMemberId);
        if (selectedMember) {
          userName = getMemberFullName(selectedMember);
          userEmail = selectedMember.email || '';
        } else {
          console.error('Member not found with ID:', formValue.existingMemberId);
          throw new Error('Membre sélectionné introuvable. Veuillez actualiser la page et réessayer.');
        }
      } else {
        userName = `${formValue.firstName} ${formValue.lastName}`;
        userEmail = formValue.email;
      }

      const reservationData: any = {
        spaceId: formValue.spaceId,
        spaceName: this.selectedSpace?.name || '',
        memberId: formValue.memberType === 'existing' ? formValue.existingMemberId?.toString() : null,
        userName: userName,
        userEmail: userEmail,
        startTime: startDateTime,
        endTime: endDateTime,
        purpose: formValue.purpose,
        status: formValue.status || 'confirmed',
        numberOfPeople: formValue.numberOfPeople || 1,
        notes: formValue.notes || ''
      };

      // Ajouter les informations du nouveau member si nécessaire
      if (formValue.memberType === 'new') {
        reservationData.firstName = formValue.firstName;
        reservationData.lastName = formValue.lastName;
        reservationData.email = formValue.email;
        reservationData.phone = formValue.phone;
        reservationData.company = formValue.company;
      }

      // Appel API pour créer ou mettre à jour la réservation
      const apiCall = this.isEditMode && this.editingReservation?.id ?
        this.reservationService.updateReservation(this.editingReservation.id, reservationData) :
        this.reservationService.createReservation(reservationData);

      apiCall.subscribe({
        next: (savedReservation) => {
          this.savingSignal.set(false);
          this.message.success(
            this.isEditMode ? 'Réservation mise à jour avec succès' : 'Réservation créée avec succès'
          );
          // Rediriger vers la page des réservations après sauvegarde
          this.router.navigate(['/reservations']);
        },
        error: (error) => {
          this.savingSignal.set(false);
          console.error('Erreur lors de la sauvegarde:', error);

          // Gestion des erreurs spécifiques
          if (error.status === 400 && error.error?.message?.includes('Conflicting reservation')) {
            this.message.error('Un conflit de réservation existe pour ce créneau horaire');
          } else if (error.status === 404) {
            this.message.error('Espace ou member introuvable');
          } else {
            this.message.error('Erreur lors de la sauvegarde de la réservation');
          }
        }
      });

      } catch (error: any) {
        this.savingSignal.set(false);
        console.error('Erreur de validation:', error);
        this.message.error(error.message || 'Erreur de validation des données');
      }
    } else {
      const validationMessage = this.errorService.getFormValidationMessage(this.reservationForm);
      this.message.warning(validationMessage);
      this.markFormGroupTouched(this.reservationForm);
    }
  }

  onCancel() {
    this.router.navigate(['/reservations']);
  }

  // Méthode pour le filtrage personnalisé des members (optionnel)
  filterMemberOption = (input: string, option: any): boolean => {
    const member = this.members.find(c => c.id === option.nzValue);
    if (!member) return false;

    const searchText = input.toLowerCase();
    return Boolean(
      member.firstName?.toLowerCase().includes(searchText) ||
      member.lastName?.toLowerCase().includes(searchText) ||
      member.email?.toLowerCase().includes(searchText) ||
      (member.company && member.company.toLowerCase().includes(searchText))
    );
  }

  // Méthodes utilitaires pour la validation
  getErrorMessage(controlName: string): string {
    return this.errorService.getErrorMessage(controlName, this.reservationForm, this.validation_messages);
  }

  isFieldInvalid(fieldName: string): boolean {
    return this.errorService.isFieldInvalid(fieldName, this.reservationForm);
  }

  // Gérer le changement de type de nouveau membre
  onNewMemberTypeChange(memberType: MemberType) {
    const companyControl = this.reservationForm.get('company');
    const iceNumberControl = this.reservationForm.get('iceNumber');
    const studentCodeControl = this.reservationForm.get('studentCode');

    // Réinitialiser tous les champs conditionnels
    companyControl?.clearValidators();
    iceNumberControl?.clearValidators();
    studentCodeControl?.clearValidators();

    // Appliquer les validations selon le type
    if (memberType === MemberType.COMPANY) {
      companyControl?.setValidators([Validators.required]);
      iceNumberControl?.setValidators([Validators.required]);
    } else if (memberType === MemberType.STUDENT) {
      studentCodeControl?.setValidators([Validators.required]);
    }

    // Mettre à jour la validité
    companyControl?.updateValueAndValidity();
    iceNumberControl?.updateValueAndValidity();
    studentCodeControl?.updateValueAndValidity();
  }



  // Méthodes utilitaires
  private combineDateTime(date: Date | null, time: Date | null): Date {
    if (!date || !time) {
      console.error('Date or time is null/undefined:', { date, time });
      throw new Error('Date and time are required');
    }

    // S'assurer que date et time sont des objets Date valides
    const validDate = date instanceof Date ? date : new Date(date);
    const validTime = time instanceof Date ? time : new Date(time);

    if (isNaN(validDate.getTime()) || isNaN(validTime.getTime())) {
      console.error('Invalid date or time:', { date, time, validDate, validTime });
      throw new Error('Invalid date or time values');
    }

    const combined = new Date(validDate);
    combined.setHours(validTime.getHours(), validTime.getMinutes(), 0, 0);
    return combined;
  }

  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  // Charger les subscriptions depuis le service
  private loadSubscriptions() {
    this.subscriptionService.getPlans().subscribe({
      next: (subscriptions: SubscriptionPlan[]) => {
        this.subscriptions = subscriptions;
      },
      error: (error: any) => {
        console.error('Error loading subscriptions:', error);
        this.subscriptions = [];
      }
    });
  }

  // Filtrer les subscriptions par type de membre
  getSubscriptionsByType(memberType: MemberType | null) {
    if (!memberType) return [];
    // Convertir MemberType en MembershipType pour la comparaison
    const membershipType = memberType as any; // Conversion temporaire
    return this.subscriptions.filter(sub =>
      sub.membershipTypes && sub.membershipTypes.includes(membershipType)
    );
  }

  protected readonly EquipmentStatus = EquipmentStatus;
}
